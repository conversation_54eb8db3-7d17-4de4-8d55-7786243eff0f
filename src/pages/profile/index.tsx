import { useState, useEffect } from 'react'
import { View, Text, Button } from '@tarojs/components'
import { Avatar, Badge } from '@nutui/nutui-react-taro'
import Taro from '@tarojs/taro'
import UserService from '../../services/user'
import './index.scss'

function Profile() {
  const [isLogin, setIsLogin] = useState(false)
  const [userInfo, setUserInfo] = useState<any>(null)
  const [showPhoneAuth, setShowPhoneAuth] = useState(false)
  const [isFirstLoad, setIsFirstLoad] = useState(true)
  const [showAuthModal, setShowAuthModal] = useState(false)

  // 配置分享功能
  Taro.useShareAppMessage(() => {
    return {
      title: '暴风回收 - 专业手机回收平台',
      path: '/pages/index/index',
      imageUrl: 'https://img12.360buyimg.com/imagetools/jfs/t1/143702/31/16654/116794/5fc6f541Edebf8a57/4138097748889987.png'
    }
  })

  // 配置分享到朋友圈
  Taro.useShareTimeline(() => {
    return {
      title: '暴风回收 - 专业手机回收平台，让闲置物品变现更简单！',
      imageUrl: 'https://img12.360buyimg.com/imagetools/jfs/t1/143702/31/16654/116794/5fc6f541Edebf8a57/4138097748889987.png'
    }
  })

  // 检查用户授权状态
  useEffect(() => {
    // 页面加载时立即检查并处理授权状态
    checkUserAuthStatus()
  }, [])

  // 页面显示时的处理（从其他页面返回时）
  Taro.useDidShow(() => {
    if (!isFirstLoad) {
      // 不是首次加载，可能是从其他页面返回，重新检查状态
      console.log('页面重新显示，检查用户状态')
      checkUserAuthStatus()
    } else {
      setIsFirstLoad(false)
    }
  })

  // 检查用户授权状态并自动获取手机号
  const checkUserAuthStatus = async () => {
    try {
      console.log('开始检查用户授权状态')

      // 检查是否已登录
      const isLoggedIn = UserService.isLoggedIn()
      const localUserInfo = UserService.getUserInfo()

      if (isLoggedIn && localUserInfo) {
        // 检查是否已授权手机号
        const hasPhoneAuth = UserService.hasPhoneAuthorization()

        if (hasPhoneAuth) {
          // 已授权手机号，正常显示用户信息
          console.log('用户已登录且已授权手机号')
          setIsLogin(true)
          setUserInfo(localUserInfo)
          setShowPhoneAuth(false)
        } else {
          // 未授权手机号，立即显示全屏授权模态框
          console.log('用户已登录但未授权手机号，立即显示授权模态框')
          setIsLogin(false)
          setShowPhoneAuth(true)

          // 立即显示全屏授权模态框
          setTimeout(() => {
            setShowAuthModal(true)
          }, 300)
        }
      } else {
        // 未登录，先进行微信登录，然后直接获取手机号
        console.log('用户未登录，开始自动登录并获取手机号流程')
        try {
          await UserService.wxLogin()
          console.log('微信登录成功，准备获取手机号')
          // 登录成功后立即显示全屏授权模态框
          setIsLogin(false)
          setShowPhoneAuth(true)

          // 立即显示全屏授权模态框
          setTimeout(() => {
            setShowAuthModal(true)
          }, 300)
        } catch (loginError) {
          console.error('自动登录失败:', loginError)
          setIsLogin(false)
          setShowPhoneAuth(true)
        }
      }
    } catch (error) {
      console.error('检查用户授权状态失败:', error)
      setIsLogin(false)
      setShowPhoneAuth(true)
    }
  }



  // 处理手机号获取成功 - 优化版本
  const handleGetPhoneNumber = async (e: any) => {
    console.log('手机号获取回调触发:', e)
    console.log('回调详情:', e.detail)

    try {
      if (e.detail.errMsg === 'getPhoneNumber:ok') {
        console.log('用户同意获取手机号，开始处理...')
        Taro.showLoading({ title: '正在获取手机号...', mask: true })

        // 调用后端接口获取手机号
        try {
          const userData = await UserService.getPhoneNumber(e.detail.code)
          console.log('手机号授权成功，用户信息:', userData)

          // 更新状态
          setIsLogin(true)
          setUserInfo(userData)
          setShowPhoneAuth(false)
          setShowAuthModal(false) // 关闭授权模态框

          Taro.hideLoading()

          // 显示成功提示，更友好
          Taro.showToast({
            title: '✅ 授权成功！',
            icon: 'success',
            duration: 1500
          })

          // 可选：震动反馈
          Taro.vibrateShort().catch(() => {})

        } catch (apiError) {
          console.error('手机号授权失败:', apiError)
          Taro.hideLoading()

          // 更详细的错误处理
          let errorMessage = '授权失败，请重试'
          if (apiError && typeof apiError === 'object') {
            errorMessage = (apiError as any).message || errorMessage
          }

          Taro.showModal({
            title: '授权失败',
            content: errorMessage + '\n\n是否重新尝试？',
            confirmText: '重试',
            cancelText: '稍后再说',
            success: (res) => {
              if (!res.confirm) {
                // 用户选择稍后，给出温和提示
                Taro.showToast({
                  title: '您可以稍后重新授权',
                  icon: 'none',
                  duration: 2000
                })
              }
            }
          })
        }
      } else if (e.detail.errMsg === 'getPhoneNumber:fail user deny') {
        console.log('用户拒绝获取手机号')

        // 更温和的处理用户拒绝的情况
        Taro.showModal({
          title: '温馨提示',
          content: '手机号授权可以帮助我们提供更好的服务体验，您可以随时在个人中心完成授权',
          confirmText: '我知道了',
          showCancel: false,
          success: () => {
            console.log('用户了解拒绝授权的后果')
          }
        })

      } else {
        console.log('获取手机号失败:', e.detail.errMsg)
        Taro.showToast({
          title: '获取手机号失败，请重试',
          icon: 'none',
          duration: 2000
        })
      }
    } catch (error) {
      console.error('手机号授权过程出错:', error)
      Taro.hideLoading()
      Taro.showToast({
        title: '系统异常，请稍后重试',
        icon: 'none',
        duration: 2000
      })
    }
  }

  const handleLogout = () => {
    Taro.clearStorageSync()
    setIsLogin(false)
    Taro.showToast({ title: '已退出登录', icon: 'success' })
  }



  // 复制微信号功能
  const handleCopyWechat = async () => {
    console.log('复制微信号功能被触发')
    const wechatId = 'baofeng_recycle'
    try {
      await Taro.setClipboardData({
        data: wechatId
      })
      Taro.showToast({ title: '微信号已复制到剪贴板', icon: 'success' })
    } catch (error) {
      console.error('复制失败:', error)
      Taro.showToast({ title: '复制失败，请重试', icon: 'none' })
    }
  }

  // 一键分享功能 - 符合微信规范
  const handleShare = () => {
    console.log('分享功能被触发')

    Taro.showActionSheet({
      itemList: ['分享小程序', '复制链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            Taro.showShareMenu({ withShareTicket: true })
            Taro.showToast({ title: '请点击右上角分享给好友', icon: 'none' })
            break
          case 1:
            Taro.setClipboardData({
              data: 'https://www.baofengrecycle.com/miniprogram'
            }).then(() => {
              Taro.showToast({ title: '链接已复制', icon: 'success' })
            })
            break
        }
      }
    })
  }



  // 联系我们项配置
  const contactItems = [
    {
      icon: '📞',
      title: '电话联系',
      rightText: '',
      onClick: () => {
        console.log('电话联系功能被触发')
        // 先显示确认对话框
        Taro.showModal({
          title: '拨打电话',
          content: '确定要拨打客服电话 18210924745 吗？',
          success: (res) => {
            if (res.confirm) {
              Taro.makePhoneCall({
                phoneNumber: '18210924745'
              }).catch(() => {
                Taro.showToast({ title: '拨打电话失败', icon: 'none' })
              })
            }
          },
        })
      }
    },
    {
      icon: '💬',
      title: '复制微信号',
      rightText: 'baofeng_recycle',
      onClick: handleCopyWechat
    },
    {
      icon: '📤',
      title: '一键分享',
      rightText: '分享小程序',
      onClick: handleShare
    }
  ]

  return (
    <View className='profile-page'>
      {/* 用户信息区域 */}
      <View className='user-section'>
        <View className='user-info'>
          {isLogin && userInfo ? (
            <>
              <Avatar size='large' url={userInfo.avatar || userInfo.avatarUrl} />
              <View className='user-details'>
                <View className='user-name'>
                  <Text className='nickname'>{userInfo.nickname || userInfo.nickName || '暴风用户'}</Text>
                  <Badge value={userInfo.level || 'VIP'} />
                </View>
                <Text className='phone'>{userInfo.phone || '手机号已授权'}</Text>
              </View>
            </>
          ) : (
            <>
              <Avatar size='large' />
              <View className='user-details'>
                {showPhoneAuth ? (
                  <>
                    <Text className='auth-title'>完善个人信息</Text>
                    <Button
                      className='login-btn primary-btn auto-auth'
                      openType='getPhoneNumber'
                      onGetPhoneNumber={handleGetPhoneNumber}
                    >
                      📱 一键获取手机号
                    </Button>
                    <Text className='login-desc'>
                      安全便捷，获取手机号后享受完整服务
                    </Text>
                  </>
                ) : (
                  <>
                    <Button
                      className='login-btn primary-btn'
                      openType='getPhoneNumber'
                      onGetPhoneNumber={handleGetPhoneNumber}
                    >
                      🚀 快速登录
                    </Button>
                    <Text className='login-desc'>
                      登录后享受专属服务
                    </Text>
                  </>
                )}
              </View>
            </>
          )}
        </View>
      </View>

      {/* 联系我们区域 - 只有授权用户才能看到 */}
      {isLogin && userInfo ? (
        <View className='contact-section'>
          <View className='contact-group'>
            {contactItems.map((item, index) => (
              <View key={index} className='contact-item' onClick={item.onClick}>
                <View className='contact-content'>
                  <View className='contact-left'>
                    <Text className='contact-icon'>{item.icon}</Text>
                    <Text className='contact-title'>{item.title}</Text>
                  </View>
                  <View className='contact-right'>
                    <Text className='contact-desc'>{item.rightText}</Text>
                    <Text className='contact-arrow'>›</Text>
                  </View>
                </View>
              </View>
            ))}
          </View>
        </View>
      ) : (
        <View className='auth-prompt'>
          <View className='auth-icon'>📱</View>
          <Text className='auth-prompt-title'>手机号授权</Text>
          <Text className='auth-prompt-text'>
            获取手机号后可享受：
            {'\n'}• 订单状态实时通知
            {'\n'}• 专属客服快速联系
            {'\n'}• 个性化服务推荐
          </Text>
        </View>
      )}





      {/* 底部按钮区域 */}
      <View className='logout-section'>
        {isLogin && userInfo ? (
          <View className='logout-btn' onClick={handleLogout}>
            <Text>退出登录</Text>
          </View>
        ) : (
          <Button
            className='login-btn-large primary-action pulse-animation'
            openType='getPhoneNumber'
            onGetPhoneNumber={handleGetPhoneNumber}
          >
            {showPhoneAuth ? '📱 立即获取手机号' : '🚀 一键快速登录'}
          </Button>
        )}
      </View>

      {/* 全屏手机号授权模态框 */}
      {showAuthModal && (
        <View className='auth-modal-overlay'>
          <View className='auth-modal'>
            <View className='auth-modal-content'>
              <View className='auth-modal-icon'>📱</View>
              <Text className='auth-modal-title'>手机号授权</Text>
              <Text className='auth-modal-desc'>
                为了提供更好的服务体验，需要获取您的手机号码
              </Text>
              <Text className='auth-modal-benefits'>
                • 订单状态实时通知{'\n'}
                • 专属客服快速联系{'\n'}
                • 个性化服务推荐
              </Text>

              <Button
                className='auth-modal-btn'
                openType='getPhoneNumber'
                onGetPhoneNumber={handleGetPhoneNumber}
              >
                立即授权
              </Button>

              <Text
                className='auth-modal-skip'
                onClick={() => setShowAuthModal(false)}
              >
                暂不授权
              </Text>
            </View>
          </View>
        </View>
      )}
    </View>
  )
}

export default Profile
