.profile-page {
  background-color: #f5f5f5;
  min-height: 100vh;

  .user-section {
    background: linear-gradient(135deg, #1890ff, #36cfc9);
    padding: 40px 20px 20px;
    color: white;

    .user-info {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      .user-details {
        margin-left: 16px;
        flex: 1;

        .user-name {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .nickname {
            font-size: 20px;
            font-weight: 600;
            margin-right: 8px;
          }
        }

        .phone {
          font-size: 14px;
          opacity: 0.9;
        }

        .login-text {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 8px;
          display: block;
        }

        .login-desc {
          font-size: 14px;
          opacity: 0.9;
        }

        .auth-title {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 12px;
          display: block;
          color: white;
        }

        .login-btn {
          background: rgba(255, 255, 255, 0.2);
          color: white;
          border: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: 20px;
          padding: 8px 16px;
          font-size: 14px;
          font-weight: 500;
          margin-top: 8px;
          backdrop-filter: blur(10px);
          transition: all 0.3s ease;

          &.primary-btn {
            background: rgba(255, 255, 255, 0.9);
            color: #1890ff;
            border: none;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            &.auto-auth {
              background: linear-gradient(135deg, #52c41a, #1890ff);
              color: white;
              font-size: 15px;
              padding: 10px 20px;
              border-radius: 25px;
              box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
              animation: pulse 2s infinite;

              &:active {
                background: linear-gradient(135deg, #389e0d, #0e7ce8);
                transform: scale(0.95);
              }
            }

            &:active {
              background: rgba(255, 255, 255, 0.8);
              transform: scale(0.98);
            }
          }

          &:active {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0.98);
          }
        }
      }
    }

    .user-stats {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 20px;
      display: flex;
      justify-content: space-around;
      align-items: center;

      .stat-item {
        text-align: center;
        flex: 1;

        .stat-number {
          font-size: 20px;
          font-weight: 600;
          display: block;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 12px;
          opacity: 0.9;
        }
      }

      .stat-divider {
        width: 1px;
        height: 30px;
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .menu-section,
  .contact-section,
  .setting-section {
    margin: 20px 0;

    .menu-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .menu-icon {
        font-size: 18px;
        margin-right: 12px;
      }

      .right-content {
        margin-left: auto;
      }
    }

    .nut-cell {
      background-color: white;
      margin-bottom: 1px;

      &:first-child {
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
      }

      &:last-child {
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
        margin-bottom: 0;
      }
    }
  }

  .contact-section {
    .contact-group {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      margin: 0 20px;
    }

    .contact-item {
      padding: 16px 20px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background-color: #f8f9fa;
      }

      .contact-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .contact-left {
        display: flex;
        align-items: center;
        flex: 1;

        .contact-icon {
          font-size: 18px;
          margin-right: 12px;
          color: #1890ff;
        }

        .contact-title {
          font-size: 16px;
          color: #333;
        }
      }

      .contact-right {
        display: flex;
        align-items: center;

        .contact-desc {
          font-size: 14px;
          color: #666;
          margin-right: 8px;
        }

        .contact-arrow {
          font-size: 16px;
          color: #ccc;
        }
      }
    }
  }

  .setting-section {
    .setting-group {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      margin: 0 20px;
    }

    .setting-item {
      padding: 16px 20px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background-color: #f8f9fa;
      }

      .setting-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .setting-left {
        display: flex;
        align-items: center;
        flex: 1;

        .setting-icon {
          font-size: 18px;
          margin-right: 12px;
        }

        .setting-title {
          font-size: 16px;
          color: #333;
        }
      }

      .setting-right {
        display: flex;
        align-items: center;

        .setting-arrow {
          font-size: 16px;
          color: #ccc;
        }
      }
    }
  }

  .auth-prompt {
    margin: 20px;
    padding: 30px 20px;
    background: linear-gradient(135deg, #f0f9ff, #e6f7ff);
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
    border: 1px solid rgba(24, 144, 255, 0.1);

    .auth-icon {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
      animation: bounce 2s infinite;
    }

    .auth-prompt-title {
      font-size: 18px;
      font-weight: 600;
      color: #1890ff;
      margin-bottom: 12px;
      display: block;
    }

    .auth-prompt-text {
      font-size: 14px;
      color: #666;
      line-height: 1.8;
      text-align: left;
      background: rgba(255, 255, 255, 0.8);
      padding: 16px;
      border-radius: 8px;
      margin-top: 12px;
    }
  }

  .logout-section {
    padding: 20px;

    .logout-btn {
      background: white;
      border-radius: 8px;
      padding: 16px;
      text-align: center;
      color: #ff4d4f;
      font-size: 16px;
      font-weight: 600;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:active {
        background: #f5f5f5;
      }
    }

    .login-btn-large {
      background: linear-gradient(135deg, #1890ff, #36cfc9);
      color: white;
      border-radius: 12px;
      padding: 18px;
      font-size: 16px;
      font-weight: 600;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
      border: none;
      width: 100%;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &.primary-action {
        background: linear-gradient(135deg, #52c41a, #1890ff);
        box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s;
        }

        &:active::before {
          left: 100%;
        }
      }

      &.pulse-animation {
        animation: pulse 2s infinite;
      }

      &:active {
        background: linear-gradient(135deg, #0e7ce8, #2fb8b2);
        transform: translateY(1px);
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
      }
    }
  }
}

// 动画定义
@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
  }
  50% {
    box-shadow: 0 4px 20px rgba(82, 196, 26, 0.5);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}
