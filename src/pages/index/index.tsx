import { useState, useEffect } from "react";
import { View, Text, Image } from "@tarojs/components";
import {
  SearchBar,
  Swiper,
  SwiperItem,
  NoticeBar,
} from "@nutui/nutui-react-taro";
import Taro from "@tarojs/taro";
import { api } from "../../services/api";
import UserService, { UserInfo } from "../../services/user";
import "./index.scss";

// 定义接口数据类型
interface NoticeItem {
  id: number;
  content: string;
  type?: string;
}

interface ContactInfo {
  phone: string;
  wechat: string;
  shareContent: {
    title: string;
    path: string;
    imageUrl?: string;
  };
}



function Index() {
  const [notices, setNotices] = useState<NoticeItem[]>([]);
  const [contactInfo, setContactInfo] = useState<ContactInfo>({
    phone: '18210924745',
    wechat: 'baofeng_recycle',
    shareContent: {
      title: '暴风回收 - 专业手机回收平台',
      path: '/pages/index/index'
    }
  });
  const [loading, setLoading] = useState(false);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [showPhoneAuthModal, setShowPhoneAuthModal] = useState(false);

  // 获取首页配置数据
  const fetchHomeConfig = async () => {
    try {
      setLoading(true);
      const config = await api.home.getConfig();
      console.log(config)
      if (config.notices) {
        setNotices(config.notices);
      }

      if (config.contact) {
        setContactInfo(config.contact);
      }
    } catch (error) {
      console.error('获取首页配置失败:', error);
      // 使用默认的公告数据
      setNotices([
        { id: 1, content: '📢 如有闲置手机请告知回收人员' },
        { id: 2, content: '🎉 新用户注册即享专属优惠' },
        { id: 3, content: '💰 高价回收，秒到账，安全可靠' }
      ]);
    } finally {
      setLoading(false);
    }
  };

  // 处理手机号获取成功
  const handleGetPhoneNumber = async (e: any) => {
    console.log('手机号获取回调触发:', e);
    console.log('回调详情:', e.detail);

    try {
      if (e.detail.errMsg === 'getPhoneNumber:ok') {
        console.log('用户同意获取手机号');
        Taro.showLoading({ title: '授权中...', mask: true });

        // 调用后端接口获取手机号
        try {
          const userData = await UserService.getPhoneNumber(e.detail.code);
          console.log('手机号授权成功，用户信息:', userData);

          // 更新状态
          setUserInfo(userData);
          setShowPhoneAuthModal(false);

          Taro.hideLoading();
          Taro.showToast({ title: '授权成功', icon: 'success' });
        } catch (apiError) {
          console.error('手机号授权失败:', apiError);
          Taro.hideLoading();
          Taro.showToast({ title: '授权失败，请重试', icon: 'none' });
        }
      } else if (e.detail.errMsg === 'getPhoneNumber:fail user deny') {
        console.log('用户拒绝获取手机号');
        Taro.showToast({ title: '需要手机号授权才能使用完整功能', icon: 'none' });
        setShowPhoneAuthModal(false);
      } else {
        console.log('获取手机号失败:', e.detail.errMsg);
        Taro.showToast({ title: '获取手机号失败', icon: 'none' });
        setShowPhoneAuthModal(false);
      }
    } catch (error) {
      console.error('手机号授权过程出错:', error);
      Taro.hideLoading();
      Taro.showToast({ title: '授权失败', icon: 'none' });
      setShowPhoneAuthModal(false);
    }
  };

  // 页面加载时获取数据和执行登录
  useEffect(() => {
    // 使用 UserService 进行自动登录
    UserService.autoLogin()
      .then((userData) => {
        console.log('登录成功，用户信息:', userData);
        setUserInfo(userData);

        // 检查是否已授权手机号
        const hasPhoneAuth = UserService.hasPhoneAuthorization();
        if (!hasPhoneAuth) {
          console.log('用户未授权手机号，显示授权提示');
          // 显示手机号授权提示
          setShowPhoneAuthModal(true);
        }

        // 登录成功后获取首页配置
        fetchHomeConfig();
      })
      .catch((error) => {
        console.error('登录失败:', error);
        // 即使登录失败也要加载首页数据
        fetchHomeConfig();
      });
  }, []);

  // 配置分享功能 - 使用动态数据
  Taro.useShareAppMessage(() => {
    return {
      title: contactInfo.shareContent.title,
      path: contactInfo.shareContent.path,
      imageUrl: contactInfo.shareContent.imageUrl,
    };
  });

  // 配置分享到朋友圈 - 使用动态数据
  Taro.useShareTimeline(() => {
    return {
      title: contactInfo.shareContent.title || "暴风回收 - 让闲置物品变现更简单！",
      imageUrl: contactInfo.shareContent.imageUrl ||
        "https://img12.360buyimg.com/imagetools/jfs/t1/143702/31/16654/116794/5fc6f541Edebf8a57/4138097748889987.png",
    };
  });

  // 轮播图数据
  const banners = [
    {
      id: 1,
      image:
        "https://img12.360buyimg.com/imagetools/jfs/t1/143702/31/16654/116794/5fc6f541Edebf8a57/4138097748889987.png",
      title: "暴风数码",
      subtitle: "高价/秒/到/账",
    },
  ];

  // 手机价格数据
  const phonePrice = [
    { id: 1, name: "三星S25 5G S9310(价格参考)12+512", price: "-50", trend: "down" },
    { id: 2, name: "三星S25 5G S9310(价格参考)12+256", price: "-50", trend: "down" },
    { id: 3, name: "OPPO A3X 5G 6+128", price: "+20", trend: "up" },
    { id: 4, name: "OPPO A5活力版12+256", price: "-30", trend: "down" },
    { id: 5, name: "OPPO A5活力版12+512", price: "-30", trend: "down" },
  ];

  const handleSearch = (value: string) => {
    Taro.navigateTo({
      url: `/pages/search/index?keyword=${encodeURIComponent(value)}`,
    });
  };

  // 电话联系功能 - 使用动态数据
  const handlePhoneCall = () => {
    const phoneNumber = contactInfo.phone;
    Taro.showModal({
      title: '拨打电话',
      content: `确定要拨打客服电话 ${phoneNumber} 吗？`,
      success: (res) => {
        if (res.confirm) {
          Taro.makePhoneCall({
            phoneNumber
          }).catch(() => {
            Taro.showToast({ title: '拨打电话失败', icon: 'none' })
          })
        }
      },
    })
  };

  // 复制微信号功能 - 使用动态数据
  const handleCopyWechat = async () => {
    const wechatId = contactInfo.wechat;
    try {
      await Taro.setClipboardData({
        data: wechatId
      })
      Taro.showToast({ title: '微信号已复制到剪贴板', icon: 'success' })
    } catch (error) {
      console.error('复制失败:', error)
      Taro.showToast({ title: '复制失败，请重试', icon: 'none' })
    }
  };
  const toCategory = () => {
    console.log('跳转到类目页面')
    Taro.switchTab({
      url: `/pages/category/index`
    });
  }

  // 测试跳转到手机详情页面
  const testPhoneDetail = () => {
    console.log('测试跳转到手机详情页面')
    Taro.navigateTo({
      url: `/pages/phone-detail/index?id=1239`
    });
  }

  // 一键分享功能 - 使用动态数据
  const handleShare = () => {
    Taro.showActionSheet({
      itemList: ['分享小程序', '复制链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            Taro.showShareMenu({ withShareTicket: true })
            Taro.showToast({ title: '请点击右上角分享给好友', icon: 'none' })
            break
          case 1:
            const shareLink = contactInfo.shareContent.imageUrl || 'https://www.baofengrecycle.com/miniprogram';
            Taro.setClipboardData({
              data: shareLink
            }).then(() => {
              Taro.showToast({ title: '链接已复制', icon: 'success' })
            })
            break
        }
      }
    })
  };

  return (
    <View className="home-page">
      {/* 用户状态提示（开发时可见） */}
      {process.env.NODE_ENV === 'development' && userInfo && (
        <View style={{
          padding: '5px 10px',
          backgroundColor: '#e6f7ff',
          fontSize: '12px',
          color: '#1890ff',
          textAlign: 'center'
        }}>
          用户已登录 {userInfo.openid ? `(OpenID: ${userInfo.openid.substring(0, 8)}...)` : '(本地登录)'}
        </View>
      )}

      {/* 搜索栏 */}
      <View className="search-section">
        <SearchBar
          placeholder="搜索想要回收的商品"
          onSearch={handleSearch}
        />
      </View>

      {/* 轮播图 */}
      <View className="banner-section">
        <Swiper autoPlay={3000} paginationVisible>
          {banners.map((banner) => (
            <SwiperItem key={banner.id}>
              <View className="banner-item">
                <Image
                  src={banner.image}
                  className="banner-image"
                  mode="aspectFill"
                />
                <View className="banner-overlay">
                  <Text className="banner-title">{banner.title}</Text>
                  <Text className="banner-subtitle">{banner.subtitle}</Text>
                </View>
              </View>
            </SwiperItem>
          ))}
        </Swiper>
      </View>

      {/* 公告栏 - 上下滚动 */}
      <View className="notice-section">
        {notices.length > 0 && (
          <NoticeBar
            text={notices.map(notice => notice.content).join('    ')}
            background="#fff7e6"
            color="#fa8c16"
            leftIcon="volume"
            scrollable
          />
        )}
      </View>

      {/* 手机报价卡片 */}
      <View className="price-card-section">
        <View className="price-card">
          <View className="price-card-left">
          {/* 背景图 */}
          </View>
          <View className="price-card-right">
            <Text className="check-price-btn" onClick={toCategory}>去查看</Text>
            <Text className="check-price-btn" onClick={testPhoneDetail} style={{marginLeft: '10px', background: '#ff6b6b'}}>测试详情</Text>
          </View>
        </View>
      </View>

      {/* 联系功能区域 */}
      <View className="contact-actions-section">
        <Text className="contact-section-title">暴风回收·当面收</Text>
        <View className="contact-actions">
          <View className="contact-action-item" onClick={handlePhoneCall}>
            <View className="action-icon-circle phone-circle">📞</View>
            <Text className="action-text">电话联系</Text>
          </View>
          <View className="contact-action-item" onClick={handleCopyWechat}>
            <View className="action-icon-circle wechat-circle">💬</View>
            <Text className="action-text">复制微信号</Text>
          </View>
          <View className="contact-action-item" onClick={handleShare}>
            <View className="action-icon-circle share-circle">📤</View>
            <Text className="action-text">一键分享</Text>
          </View>
        </View>
      </View>

      {/* 门面当面卖区域 */}
      <View className="store-section">
        <View className="store-header">
          <Text className="store-title">门面当面卖</Text>
          <View className="store-badge">HOT</View>
        </View>
        <View className="store-content">
          <View className="store-image">
            <Image
              src="https://img12.360buyimg.com/imagetools/jfs/t1/143702/31/16654/116794/5fc6f541Edebf8a57/4138097748889987.png"
              className="store-photo"
              mode="aspectFill"
            />
          </View>
          <View className="store-info">
            <View className="contact-row">
              <Text className="contact-label">销售电话/微信：</Text>
              <Text className="contact-number">{contactInfo.phone}</Text>
              <View className="contact-icons">
                <View className="contact-icon phone-green" onClick={handlePhoneCall}>📞</View>
                <View className="contact-icon wechat-green" onClick={handleCopyWechat}>💬</View>
              </View>
            </View>
            <View className="contact-row">
              <Text className="contact-label">回收电话/微信：</Text>
              <Text className="contact-number">{contactInfo.phone}</Text>
              <View className="contact-icons">
                <View className="contact-icon phone-green" onClick={handlePhoneCall}>📞</View>
                <View className="contact-icon wechat-green" onClick={handleCopyWechat}>💬</View>
              </View>
            </View>
            <View className="address-row">
              <Text className="address-text">四川省成都市童子区装修城4F</Text>
              <View className="location-icon" onClick={() => {}}>📍</View>
            </View>
            <Text className="store-number">统一楼5032-5033号</Text>
          </View>
        </View>
      </View>

      {/* 手机价格涨跌榜 */}
      <View className="price-list-section">
        <Text className="price-list-title">视频机价格涨跌榜</Text>
        <View className="price-list">
          {phonePrice.map((phone) => (
            <View key={phone.id} className="price-item">
              <Text className="phone-name">{phone.name}</Text>
              <View className="price-change">
                <Text className={`price-trend ${phone.trend}`}>
                  {phone.trend === 'up' ? '📈' : '📉'} {phone.price}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </View>


    </View>
  );
}

export default Index;
