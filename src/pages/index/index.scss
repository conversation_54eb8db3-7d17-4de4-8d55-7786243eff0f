.home-page {
  background-color: #f5f5f5;
  min-height: 100vh;

  .search-section {
    background: white;
    padding: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .banner-section {
    margin: 10px 20px;
    border-radius: 12px;
    overflow: hidden;

    .banner-item {
      position: relative;
      height: 160px;

      .banner-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .banner-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
        padding: 20px;

        .banner-title {
          color: white;
          font-size: 24px;
          font-weight: 700;
          margin-bottom: 4px;
        }

        .banner-subtitle {
          color: white;
          font-size: 14px;
          font-weight: 400;
          opacity: 0.9;
        }
      }
    }
  }

  .notice-section {
    margin: 10px 20px;
    border-radius: 8px;
    overflow: hidden;
  }

  .price-card-section {
    margin: 10px 20px;

    .price-card {
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      border-radius: 12px;
      padding: 16px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);

      .price-card-left {
        display: flex;
        align-items: center;

        .phone-icon {
          font-size: 32px;
          margin-right: 12px;
        }

        .price-info {
          .price-title {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 2px;
          }

          .price-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
          }
        }
      }

      .price-card-right {
        .check-price-btn {
          background: rgba(255, 255, 255, 0.2);
          color: white;
          padding: 6px 16px;
          border-radius: 20px;
          font-size: 14px;
          font-weight: 500;
          border: 1px solid rgba(255, 255, 255, 0.3);
        }
      }
    }
  }

  .contact-actions-section {
    margin: 10px 20px;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .contact-section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      text-align: center;
      margin-bottom: 20px;
    }

    .contact-actions {
      display: flex;
      justify-content: space-around;
      align-items: center;

      .contact-action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px 12px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.95);
        }

        .action-icon-circle {
          width: 50px;
          height: 50px;
          border-radius: 25px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20px;
          margin-bottom: 8px;
          border: 2px solid #e8e8e8;
          background: white;

          &.phone-circle {
            color: #52c41a;
            border-color: #52c41a;
          }

          &.wechat-circle {
            color: #1890ff;
            border-color: #1890ff;
          }

          &.share-circle {
            color: #fa8c16;
            border-color: #fa8c16;
          }
        }

        .action-text {
          font-size: 12px;
          color: #666;
          font-weight: 500;
          text-align: center;
        }
      }
    }
  }

  .store-section {
    margin: 10px 20px;
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .store-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .store-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-right: 8px;
      }

      .store-badge {
        background: #ff4d4f;
        color: white;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: 600;
      }
    }

    .store-content {
      display: flex;

      .store-image {
        width: 80px;
        height: 80px;
        margin-right: 12px;

        .store-photo {
          width: 100%;
          height: 100%;
          border-radius: 8px;
          object-fit: cover;
        }
      }

      .store-info {
        flex: 1;

        .contact-row {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .contact-label {
            font-size: 12px;
            color: #666;
            margin-right: 4px;
          }

          .contact-number {
            font-size: 12px;
            color: #333;
            font-weight: 500;
            margin-right: 8px;
          }

          .contact-icons {
            display: flex;
            gap: 6px;

            .contact-icon {
              width: 24px;
              height: 24px;
              border-radius: 12px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              cursor: pointer;

              &.phone-green {
                background: #52c41a;
                color: white;
              }

              &.wechat-green {
                background: #1890ff;
                color: white;
              }
            }
          }
        }

        .address-row {
          display: flex;
          align-items: center;
          margin-bottom: 4px;

          .address-text {
            font-size: 12px;
            color: #666;
            flex: 1;
            margin-right: 8px;
          }

          .location-icon {
            width: 24px;
            height: 24px;
            border-radius: 12px;
            background: #1890ff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
          }
        }

        .store-number {
          font-size: 12px;
          color: #666;
        }
      }
    }
  }

  .price-list-section {
    margin: 10px 20px;
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .price-list-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
    }

    .price-list {
      .price-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .phone-name {
          font-size: 14px;
          color: #333;
          flex: 1;
          margin-right: 12px;
          line-height: 1.4;
        }

        .price-change {
          .price-trend {
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 4px;

            &.up {
              color: #ff4d4f;
            }

            &.down {
              color: #52c41a;
            }
          }
        }
      }
    }
  }


}
