import Taro from '@tarojs/taro'
import { mockHomeConfig, mockApiDelay } from '../mock/homeConfig'

// API 基础配置
const BASE_URL = 'http://localhost:3000' // 替换为实际的 API 地址
const TIMEOUT = 10000

// 获取JWT Token的辅助函数
const getAccessToken = () => {
  try {
    // 优先从 userInfo 中获取 accessToken
    const userInfo = Taro.getStorageSync('userInfo')
    if (userInfo && userInfo.accessToken) {
      return userInfo.accessToken
    }

    // 兼容旧版本，从 token 字段获取
    const token = Taro.getStorageSync('token')
    if (token) {
      return token
    }

    return null
  } catch (error) {
    console.error('获取访问令牌失败:', error)
    return null
  }
}

// 请求拦截器
const request = (options: any) => {
  const { url, data, method = 'GET', header = {} } = options

  // 获取JWT访问令牌
  const accessToken = getAccessToken()

  // 构建请求头
  const requestHeader = {
    'Content-Type': 'application/json',
    ...header
  }

  // 如果有accessToken，添加Authorization头进行JWT校验
  if (accessToken) {
    requestHeader['Authorization'] = `Bearer ${accessToken}`
  }

  return Taro.request({
    url: BASE_URL + url,
    data,
    method,
    timeout: TIMEOUT,
    header: requestHeader
  }).then(response => {
    const { statusCode, data: responseData } = response

    if (statusCode >= 200 && statusCode < 300) {
      return responseData
    } else if (statusCode === 401) {
      // JWT令牌过期或无效，清除本地存储
      console.warn('JWT令牌无效，清除本地用户信息')
      Taro.removeStorageSync('userInfo')
      Taro.removeStorageSync('token')

      // 显示重新登录提示
      Taro.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      })

      throw new Error('JWT令牌无效')
    } else {
      throw new Error(`请求失败: ${statusCode}`)
    }
  }).catch(error => {
    console.error('API请求错误:', error)

    // 如果不是JWT令牌错误，显示通用网络错误
    if (!error.message.includes('JWT令牌无效')) {
      Taro.showToast({
        title: '网络请求失败',
        icon: 'none'
      })
    }

    throw error
  })
}

// API 接口定义
export const api = {
  // 用户相关
  user: {
    // 获取用户信息
    getProfile: () => request({ url: '/user/profile' }),

    // 更新用户信息
    updateProfile: (data: any) => request({
      url: '/user/profile',
      method: 'PUT',
      data
    }),

    // 用户登录
    login: (data: { phone: string; code: string }) => request({
      url: '/user/login',
      method: 'POST',
      data
    }),

    // 微信登录
    wxLogin: (data: { code: string }) => request({
      url: '/api/v1/wechat/login',
      method: 'POST',
      data
    }),

    // 获取微信小程序用户手机号
    getPhoneNumber: (data: { code: string }) => request({
      url: '/api/v1/wechat/phone',
      method: 'POST',
      data
    }),

    // 发送验证码
    sendCode: (phone: string) => request({
      url: '/user/send-code',
      method: 'POST',
      data: { phone }
    })
  },

  // 商品分类相关
  category: {
    // 获取所有分类
    getAll: () => request({ url: '/categories' }),

    // 获取分类树
    getTree: () => request({ url: '/api/v1/categories/tree' }),

    // 获取分类下的商品
    getProducts: (categoryId: string) => request({
      url: `/categories/${categoryId}/products`
    }),

    // 搜索商品
    search: (keyword: string) => request({
      url: '/products/search',
      data: { keyword }
    })
  },

  // 价格排行榜相关
  ranking: {
    // 获取涨价榜
    getRising: () => request({ url: '/ranking/rising' }),

    // 获取降价榜
    getFalling: () => request({ url: '/ranking/falling' }),

    getTree:()=> request({ url: '/categories/tree' }),
    // 获取热门榜
    getHot: () => request({ url: '/ranking/hot' })
  },

  // 回收订单相关
  order: {
    // 创建回收订单
    create: (data: any) => request({
      url: '/orders',
      method: 'POST',
      data
    }),

    // 获取用户订单列表
    getList: (status?: string) => request({
      url: '/orders',
      data: status ? { status } : {}
    }),

    // 获取订单详情
    getDetail: (orderId: string) => request({
      url: `/orders/${orderId}`
    }),

    // 取消订单
    cancel: (orderId: string) => request({
      url: `/orders/${orderId}/cancel`,
      method: 'PUT'
    })
  },

  // 估价相关
  valuation: {
    // 获取商品估价
    getPrice: (data: {
      productId: string;
      condition: string;
      accessories: string[];
    }) => request({
      url: '/valuation/price',
      method: 'POST',
      data
    }),

    // 提交估价申请
    submit: (data: any) => request({
      url: '/valuation/submit',
      method: 'POST',
      data
    })
  },

  // 地址管理
  address: {
    // 获取用户地址列表
    getList: () => request({ url: '/addresses' }),

    // 添加地址
    add: (data: any) => request({
      url: '/addresses',
      method: 'POST',
      data
    }),

    // 更新地址
    update: (id: string, data: any) => request({
      url: `/addresses/${id}`,
      method: 'PUT',
      data
    }),

    // 删除地址
    delete: (id: string) => request({
      url: `/addresses/${id}`,
      method: 'DELETE'
    }),

    // 设置默认地址
    setDefault: (id: string) => request({
      url: `/addresses/${id}/default`,
      method: 'PUT'
    })
  },

  // 首页配置相关
  home: {
    // 获取首页配置信息（包含公告、联系方式、分享内容等）
    getConfig: () => {
      // 在开发环境下使用模拟数据
      if (process.env.NODE_ENV === 'development') {
        return mockApiDelay(mockHomeConfig, 800);
      }
      return request({ url: '/home/<USER>' });
    },

    // 获取公告列表
    getNotices: () => {
      if (process.env.NODE_ENV === 'development') {
        return mockApiDelay(mockHomeConfig.notices, 500);
      }
      return request({ url: '/home/<USER>' });
    },

    // 获取轮播图
    getBanners: () => {
      if (process.env.NODE_ENV === 'development') {
        return mockApiDelay(mockHomeConfig.banners, 500);
      }
      return request({ url: '/home/<USER>' });
    },

    // 获取联系方式
    getContactInfo: () => {
      if (process.env.NODE_ENV === 'development') {
        return mockApiDelay(mockHomeConfig.contact, 500);
      }
      return request({ url: '/home/<USER>' });
    }
  }
}

export default api
